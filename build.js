#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建智能抢单助手...');

// 检查必要文件
const requiredFiles = [
  'main.js',
  'index.html', 
  'grabber.js',
  'package.json'
];

console.log('📋 检查必要文件...');
for (const file of requiredFiles) {
  if (!fs.existsSync(file)) {
    console.error(`❌ 缺少必要文件: ${file}`);
    process.exit(1);
  }
  console.log(`✅ ${file}`);
}

// 创建图标占位符（如果不存在）
if (!fs.existsSync('assets')) {
  fs.mkdirSync('assets', { recursive: true });
}

// 检查是否有图标文件，如果没有则创建占位符
const iconFiles = ['assets/icon.png', 'assets/icon.ico', 'assets/icon.icns'];
iconFiles.forEach(iconFile => {
  if (!fs.existsSync(iconFile)) {
    console.log(`⚠️  创建图标占位符: ${iconFile}`);
    fs.writeFileSync(iconFile, '# 图标占位符 - 请替换为真实图标文件');
  }
});

// 获取构建目标
const target = process.argv[2] || 'win';
const validTargets = ['win', 'mac', 'linux', 'all'];

if (!validTargets.includes(target)) {
  console.error(`❌ 无效的构建目标: ${target}`);
  console.log(`✅ 有效目标: ${validTargets.join(', ')}`);
  process.exit(1);
}

console.log(`🎯 构建目标: ${target}`);

try {
  let buildCommand;
  
  switch (target) {
    case 'win':
      buildCommand = 'npm run build:win';
      console.log('🪟 构建 Windows 版本...');
      break;
    case 'mac':
      buildCommand = 'npm run build:mac';
      console.log('🍎 构建 macOS 版本...');
      break;
    case 'linux':
      buildCommand = 'npm run build:linux';
      console.log('🐧 构建 Linux 版本...');
      break;
    case 'all':
      buildCommand = 'npm run build';
      console.log('🌍 构建所有平台版本...');
      break;
  }
  
  console.log(`📦 执行构建命令: ${buildCommand}`);
  execSync(buildCommand, { stdio: 'inherit' });
  
  console.log('🎉 构建完成！');
  console.log('📁 输出目录: ./dist');
  
  // 显示构建结果
  if (fs.existsSync('dist')) {
    console.log('\n📋 构建文件:');
    const files = fs.readdirSync('dist');
    files.forEach(file => {
      const filePath = path.join('dist', file);
      const stats = fs.statSync(filePath);
      const size = (stats.size / 1024 / 1024).toFixed(2);
      console.log(`  📄 ${file} (${size} MB)`);
    });
  }
  
} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}

console.log('\n✨ 构建脚本执行完成！');
