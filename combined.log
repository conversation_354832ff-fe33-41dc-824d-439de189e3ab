2025-06-07T15:12:47.018Z [INFO] 开始自动抢单 {"uid":"1","interval":5000,"allowedAreas":"所有区域"}
2025-06-07T15:12:47.020Z [INFO] 正在获取订单列表... 
2025-06-07T15:12:52.205Z [INFO] 正在获取订单列表... 
2025-06-07T15:12:57.332Z [INFO] 正在获取订单列表... 
2025-06-07T15:12:58.874Z [INFO] 收到停止抢单请求 
2025-06-07T15:13:02.493Z [INFO] 自动抢单结束 
2025-06-07T15:14:00.961Z [INFO] 开始自动抢单 {"uid":"11111","interval":5000,"allowedAreas":"所有区域"}
2025-06-07T15:14:00.961Z [INFO] 正在获取订单列表... 
2025-06-07T15:14:05.062Z [INFO] 收到停止抢单请求 
2025-06-07T15:14:06.128Z [INFO] 自动抢单结束 
2025-06-07T15:14:39.904Z [INFO] 开始自动抢单 {"uid":"11111","interval":5000,"allowedAreas":"所有区域"}
2025-06-07T15:14:39.904Z [INFO] 正在获取订单列表... 
2025-06-07T15:14:45.042Z [INFO] 正在获取订单列表... 
2025-06-07T15:14:48.118Z [INFO] 收到停止抢单请求 
2025-06-07T15:14:50.210Z [INFO] 自动抢单结束 
2025-06-07T15:16:18.474Z [INFO] 开始自动抢单 {"uid":"11111","interval":5000,"allowedAreas":"所有区域"}
2025-06-07T15:16:18.474Z [INFO] 正在获取订单列表... 
2025-06-07T15:16:23.600Z [INFO] 正在获取订单列表... 
2025-06-07T15:16:25.342Z [INFO] 收到停止抢单请求 
2025-06-07T15:16:28.713Z [INFO] 自动抢单结束 
2025-06-07T15:16:55.416Z [INFO] 开始自动抢单 {"uid":"1","interval":5000,"allowedAreas":"所有区域"}
2025-06-07T15:16:55.416Z [INFO] 正在获取订单列表... 
2025-06-07T15:17:00.568Z [INFO] 正在获取订单列表... 
2025-06-07T15:17:05.686Z [INFO] 正在获取订单列表... 
2025-06-07T15:17:05.739Z [INFO] 收到停止抢单请求 
2025-06-07T15:17:05.802Z [INFO] 自动抢单结束 
2025-06-07T15:18:16.275Z [INFO] 开始自动抢单 {"uid":"1","interval":5000,"allowedAreas":"所有区域"}
2025-06-07T15:18:16.275Z [INFO] 正在获取订单列表... 
2025-06-07T15:18:21.453Z [INFO] 正在获取订单列表... 
2025-06-07T15:18:26.571Z [INFO] 正在获取订单列表... 
2025-06-07T15:18:38.841Z [INFO] 开始自动抢单 {"uid":"1","interval":5000,"allowedAreas":"所有区域"}
2025-06-07T15:18:38.842Z [INFO] 正在获取订单列表... 
2025-06-07T15:18:38.997Z [INFO] 获取订单列表成功 {"code":4,"msg":"登陆过期","data":[],"count":0,"other":[],"server":"10.23.55.5"}
2025-06-07T15:18:44.000Z [INFO] 正在获取订单列表... 
2025-06-07T15:18:44.098Z [INFO] 获取订单列表成功 {"code":4,"msg":"登陆过期","data":[],"count":0,"other":[],"server":"10.23.55.4"}
2025-06-07T15:18:46.206Z [INFO] 收到停止抢单请求 
2025-06-07T15:18:49.102Z [INFO] 自动抢单结束 
2025-06-07T15:29:44.206Z [ERROR] Token验证失败: 登录已过期 
2025-06-07T15:29:44.208Z [ERROR] Token验证请求失败: {"error":"Unhandled error. ('Token已过期，请重新获取授权Token')"}
2025-06-07T15:30:35.438Z [ERROR] Token验证失败: 登录已过期 
2025-06-07T15:31:43.564Z [INFO] Token验证成功 
2025-06-07T15:31:44.455Z [WARN] Token验证返回异常状态 {"code":2,"msg":"操作过快"}
2025-06-07T15:31:45.373Z [INFO] Token验证成功 
2025-06-07T15:31:48.301Z [INFO] Token验证成功 
2025-06-07T15:31:48.998Z [WARN] Token验证返回异常状态 {"code":2,"msg":"操作过快"}
2025-06-07T15:31:49.821Z [INFO] Token验证成功 
2025-06-07T15:53:20.989Z [ERROR] Token验证失败: 登录已过期 
2025-06-07T15:53:20.992Z [INFO] 开始自动抢单 {"uid":"test_uid","interval":10000,"allowedAreas":"所有区域"}
2025-06-07T15:53:20.993Z [INFO] 正在获取订单列表... 
2025-06-07T15:53:21.079Z [INFO] 获取订单列表成功 {"code":4,"msg":"登陆过期","data":[],"count":0,"other":[],"server":"10.23.55.3"}
2025-06-07T15:53:22.993Z [INFO] 收到停止抢单请求 
2025-06-07T15:53:26.530Z [ERROR] Token验证失败: 登录已过期 
2025-06-07T15:53:26.533Z [INFO] 开始自动抢单 {"uid":"test_uid","interval":10000,"allowedAreas":"所有区域"}
2025-06-07T15:53:26.534Z [INFO] 正在获取订单列表... 
2025-06-07T15:53:26.650Z [INFO] 获取订单列表成功 {"code":4,"msg":"登陆过期","data":[],"count":0,"other":[],"server":"10.23.55.4"}
2025-06-07T15:53:28.534Z [INFO] 收到停止抢单请求 
2025-06-07T15:53:36.652Z [INFO] 自动抢单结束 
2025-06-07T15:55:17.551Z [INFO] Token验证成功 
2025-06-07T15:55:29.926Z [INFO] 开始自动抢单 {"uid":"12309032","interval":5000,"allowedAreas":"所有区域"}
2025-06-07T15:55:29.926Z [INFO] 正在获取订单列表... 
2025-06-07T15:55:30.066Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.6"}
2025-06-07T15:55:35.070Z [INFO] 正在获取订单列表... 
2025-06-07T15:55:35.194Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[{"id":"12158092023","user_id":null,"classify":0,"goods_id":37,"sl":null,"hj":null,"update_time":"2025-06-07 23:55:05","service_zd":0,"game_area":"微信","pay_beizhu":"","pay_texta":"老板拒绝联系","pay_textb":"糕冷傲蝶","gg_img":"","gg_title":"新赛季保底单","g_title":"","sytext":"订单编号：12158092023"}],"count":0,"other":[],"server":"10.23.55.5"}
2025-06-07T15:55:35.194Z [INFO] 发现 1 个订单，开始过滤和抢单 
2025-06-07T15:55:35.194Z [INFO] 符合条件的订单 1: {"id":"12158092023","title":"新赛季保底单","area":"微信","goods_id":37,"updateTime":"2025-06-07 23:55:05"}
2025-06-07T15:55:43.380Z [INFO] 正在获取订单列表... 
2025-06-07T15:55:43.503Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.2"}
2025-06-07T15:55:48.507Z [INFO] 正在获取订单列表... 
2025-06-07T15:55:48.644Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.5"}
2025-06-07T15:55:51.811Z [INFO] 收到停止抢单请求 
2025-06-07T15:55:53.646Z [INFO] 自动抢单结束 
2025-06-07T16:06:02.397Z [INFO] Token验证成功 
2025-06-07T16:06:04.813Z [INFO] 开始自动抢单 {"uid":"12309032","interval":1000,"allowedAreas":"所有区域"}
2025-06-07T16:06:04.813Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:04.986Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.2"}
2025-06-07T16:06:05.990Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:06.125Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.2"}
2025-06-07T16:06:07.129Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:07.262Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.3"}
2025-06-07T16:06:08.267Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:08.405Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.3"}
2025-06-07T16:06:09.407Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:09.543Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.7"}
2025-06-07T16:06:10.546Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:10.671Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.7"}
2025-06-07T16:06:11.676Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:11.799Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.8"}
2025-06-07T16:06:12.802Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:12.924Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.2"}
2025-06-07T16:06:13.927Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:14.052Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.2"}
2025-06-07T16:06:15.053Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:15.188Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.3"}
2025-06-07T16:06:16.191Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:16.268Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.5"}
2025-06-07T16:06:17.272Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:17.393Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.4"}
2025-06-07T16:06:18.396Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:18.521Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.4"}
2025-06-07T16:06:19.525Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:19.650Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.5"}
2025-06-07T16:06:20.653Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:20.785Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.7"}
2025-06-07T16:06:21.790Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:21.911Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.6"}
2025-06-07T16:06:22.915Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:23.037Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.8"}
2025-06-07T16:06:24.039Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:24.171Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.8"}
2025-06-07T16:06:25.173Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:25.313Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.2"}
2025-06-07T16:06:26.316Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:26.390Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.4"}
2025-06-07T16:06:27.394Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:27.523Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.3"}
2025-06-07T16:06:28.527Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:28.657Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.3"}
2025-06-07T16:06:29.663Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:29.800Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.5"}
2025-06-07T16:06:30.805Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:30.938Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.6"}
2025-06-07T16:06:31.942Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:32.071Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[{"id":"12159106500","user_id":null,"classify":0,"goods_id":73,"sl":null,"hj":null,"update_time":"2025-06-08 00:06:06","service_zd":0,"game_area":"微信","pay_beizhu":"","pay_texta":"老板拒绝联系","pay_textb":"DG仙境","gg_img":"","gg_title":"新赛季保底单","g_title":"","sytext":"订单编号：12159106500"}],"count":0,"other":[],"server":"10.23.55.7"}
2025-06-07T16:06:32.072Z [INFO] 发现 1 个订单，开始过滤和抢单 
2025-06-07T16:06:32.072Z [INFO] 符合条件的订单 1: {"id":"12159106500","title":"新赛季保底单","area":"微信","goods_id":73,"updateTime":"2025-06-08 00:06:06"}
2025-06-07T16:06:39.553Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:39.630Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.5"}
2025-06-07T16:06:40.630Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:40.709Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.3"}
2025-06-07T16:06:41.712Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:41.790Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.8"}
2025-06-07T16:06:42.795Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:42.874Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.6"}
2025-06-07T16:06:43.878Z [INFO] 正在获取订单列表... 
2025-06-07T16:06:44.048Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.7"}
2025-06-07T16:06:44.617Z [INFO] 收到停止抢单请求 
2025-06-07T16:06:45.051Z [INFO] 自动抢单结束 
2025-06-07T16:08:12.628Z [INFO] Token验证成功 
2025-06-07T16:25:39.294Z [INFO] Token验证成功 
2025-06-07T16:25:40.933Z [INFO] 开始自动抢单 {"uid":"12309032","interval":1000,"allowedAreas":"所有区域"}
2025-06-07T16:25:40.933Z [INFO] 正在获取订单列表... 
2025-06-07T16:25:41.016Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.2"}
2025-06-07T16:25:42.021Z [INFO] 正在获取订单列表... 
2025-06-07T16:25:42.122Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.5"}
2025-06-07T16:25:43.125Z [INFO] 正在获取订单列表... 
2025-06-07T16:25:43.216Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.3"}
2025-06-07T16:25:44.220Z [INFO] 正在获取订单列表... 
2025-06-07T16:25:44.309Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.6"}
2025-06-07T16:25:45.314Z [INFO] 正在获取订单列表... 
2025-06-07T16:25:45.399Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.7"}
2025-06-07T16:25:46.403Z [INFO] 正在获取订单列表... 
2025-06-07T16:25:46.494Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[],"count":0,"other":[],"server":"10.23.55.8"}
2025-06-07T16:25:47.495Z [INFO] 正在获取订单列表... 
2025-06-07T16:25:47.586Z [INFO] 获取订单列表成功 {"code":0,"msg":"获取成功","data":[{"id":"12159125600","user_id":null,"classify":0,"goods_id":85,"sl":null,"hj":null,"update_time":"2025-06-08 00:25:22","service_zd":0,"game_area":"微信","pay_beizhu":"地图打烽火荣都","pay_texta":"老板拒绝联系","pay_textb":"DG仙境","gg_img":"","gg_title":"新赛季保底单","g_title":"","sytext":"订单编号：12159125600"}],"count":0,"other":[],"server":"10.23.55.4"}
2025-06-07T16:25:47.586Z [INFO] 发现 1 个订单，开始过滤和抢单 
2025-06-07T16:25:47.586Z [INFO] 符合条件的订单 1: {"id":"12159125600","title":"新赛季保底单","area":"微信","goods_id":85,"updateTime":"2025-06-08 00:25:22"}
2025-06-07T16:25:50.731Z [INFO] 🎉 抢单成功！订单ID: 12159125600 {"response":{"code":0,"msg":"接单成功","data":[],"count":0,"other":[],"server":"10.23.55.6"}}
2025-06-07T16:25:50.732Z [INFO] 收到停止抢单请求 
2025-06-07T16:25:50.732Z [INFO] 自动抢单结束 
2025-06-07T16:33:30.934Z [INFO] Token验证成功 
2025-06-07T16:33:32.597Z [INFO] Token验证成功 
2025-06-07T16:33:33.932Z [INFO] Token验证成功 
2025-06-07T16:33:48.186Z [INFO] Token验证成功 
