const { contextBridge, ipc<PERSON>enderer } = require('electron');

// 通过 contextBridge 安全地暴露 API 给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 获取应用版本
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // 获取系统信息
  getSystemInfo: () => ({
    platform: process.platform,
    arch: process.arch,
    versions: process.versions
  }),

  // 打开外部链接
  openExternal: (url) => ipcRenderer.invoke('open-external', url),

  // 显示消息框
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),

  // 监听主进程消息
  onMessage: (callback) => {
    ipcRenderer.on('main-message', (event, ...args) => callback(...args));
  },

  // 发送消息到主进程
  sendMessage: (channel, data) => {
    ipcRenderer.send(channel, data);
  },

  // 移除监听器
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
});

// 在窗口加载完成后执行
window.addEventListener('DOMContentLoaded', () => {
  console.log('Preload script loaded');
  
  // 可以在这里添加一些初始化逻辑
  const replaceText = (selector, text) => {
    const element = document.getElementById(selector);
    if (element) element.innerText = text;
  };

  // 更新版本信息（如果页面中有对应元素）
  for (const dependency of ['chrome', 'node', 'electron']) {
    replaceText(`${dependency}-version`, process.versions[dependency]);
  }
});
