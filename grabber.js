const axios = require("axios");
const winston = require("winston");
const { EventEmitter } = require("events");
const { log } = require("console");

class OrderGrabber extends EventEmitter {
  constructor() {
    super();
    this.isRunning = false;
    this.grabAttempts = 0;
    this.maxGrabAttempts = 300;
    
    // 配置日志记录器
    this.logger = winston.createLogger({
      level: "info",
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ timestamp, level, message, ...meta }) => {
          const metaStr = Object.keys(meta).length ? JSON.stringify(meta) : '';
          return `${timestamp} [${level.toUpperCase()}] ${message} ${metaStr}`;
        })
      ),
      transports: [
        new winston.transports.File({ filename: "error.log", level: "error" }),
        new winston.transports.File({ filename: "combined.log" }),
        new winston.transports.Console({
          format: winston.format.printf(({ timestamp, level, message, ...meta }) => {
            const metaStr = Object.keys(meta).length ? JSON.stringify(meta) : '';
            return `${timestamp} [${level.toUpperCase()}] ${message} ${metaStr}`;
          })
        }),
      ],
    });
  }


  //验证Token是否过期



  // 验证Token有效性
  async validateToken(token) {
    try {
      const response = await axios.request({
        method: "GET",
        url: "https://api.9100dianjing.cn/wxmini/my/partner/list/new",
        headers: {
          Authorization: `Bearer ${token}`,
          "User-Agent": "Mozilla/5.0 (iPad; CPU OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.59(0x18003b2e) NetType/WIFI Language/zh_CN",
          "X-Pwid": "9100",
          "Content-Type": "application/json",
          Host: "api.9100dianjing.cn",
          Referer: "https://servicewechat.com/wx7ff158ed3cf27972/14/page-frame.html",
          Accept: "*/*",
          "Accept-Encoding": "gzip, deflate, br",
          Connection: "keep-alive",
          "Cache-Control": "no-cache",
        },
      });

      // 检查响应状态
      if (response.data) {
        const { code, msg } = response.data;

        if (code === 4 && msg === "登陆过期") {
          this.logger.error("Token验证失败: 登录已过期");
          return {
            valid: false,
            code: code,
            message: msg,
            reason: "登录已过期"
          };
        } else if (code === 0) {
          this.logger.info("Token验证成功");
          return {
            valid: true,
            code: code,
            message: msg || "Token有效",
            data: response.data
          };
        } else {
          this.logger.warn("Token验证返回异常状态", { code, msg });
          return {
            valid: false,
            code: code,
            message: msg || "未知错误",
            reason: "API返回异常状态"
          };
        }
      } else {
        this.logger.error("Token验证失败: 无响应数据");
        return {
          valid: false,
          code: -1,
          message: "无响应数据",
          reason: "服务器无响应"
        };
      }
    } catch (error) {
      this.logger.error("Token验证请求失败:", { error: error.message });
      return {
        valid: false,
        code: -1,
        message: error.message,
        reason: "网络请求失败"
      };
    }
  }

  // 获取订单列表
  async getOrderList(uid, token) {
    try {
      const response = await axios.request({
        method: "GET",
        url: "https://api.9100dianjing.cn/wxmini/my/partner/list/new",
        params: { uid, type: "" },
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c33)XWEB/13639",
          "x-pwid": "9100",
          xweb_xhr: "1",
          authorization: `Bearer ${token}`,
          "content-type": "application/json",
          "sec-fetch-site": "cross-site",
          "sec-fetch-mode": "cors",
          "sec-fetch-dest": "empty",
          referer:
            "https://servicewechat.com/wx7ff158ed3cf27972/14/page-frame.html",
          "accept-language": "zh-CN,zh;q=0.9",
          priority: "u=1, i",
          Accept: "*/*",
          "Accept-Encoding": "gzip, deflate, br",
          Connection: "keep-alive",
          "Cache-Control": "no-cache",
          Host: "api.9100dianjing.cn",
        },
      });
      return response.data;
    } catch (error) {
      this.logger.error("获取订单列表失败:", { error: error.message });
      this.emit('error', `获取订单列表失败: ${error.message}`);
      throw error;
    }
  }

  // 抢单
  async grabOrder(orderId, token) {
    try {
      const response = await axios.request({
        method: "POST",
        url: "https://api.9100dianjing.cn/wxmini/my/partner/getorder",
        headers: {
          Referer:
            "https://servicewechat.com/wx7ff158ed3cf27972/14/page-frame.html",
          Authorization: `Bearer ${token}`,
          "X-Pwid": "9100",
          Host: "api.9100dianjing.cn",
          "User-Agent":
            "Mozilla/5.0 (iPad; CPU OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.59(0x18003b2e) NetType/WIFI Language/zh_CN",
          Accept: "*/*",
          "Accept-Encoding": "gzip, deflate, br",
          Connection: "keep-alive",
          "Content-Type": "application/json",
          "Cache-Control": "no-cache",
        },
        data: { order_id: orderId },
      });
      return response.data;
    } catch (error) {
      this.logger.error("抢单失败:", { error: error.message, orderId });
      this.emit('error', `抢单失败: ${error.message}`);
      throw error;
    }
  }

  // 自动抢单主函数
  async startAutoGrab(uid, token, interval = 5000, allowedAreas = null) {
    if (this.isRunning) {
      throw new Error("抢单程序已在运行中");
    }

    // 首先验证Token有效性
    this.emit('log', "正在验证Token有效性...");
    const tokenValidation = await this.validateToken(token);

    if (!tokenValidation.valid) {
      const errorMsg = `Token验证失败: ${tokenValidation.message} (${tokenValidation.reason})`;
      this.emit('error', errorMsg);
      this.logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    this.emit('log', "Token验证成功，开始自动抢单");
    this.isRunning = true;
    this.emit('statusChange', { isRunning: true, message: "开始自动抢单" });
    this.logger.info("开始自动抢单", { uid, interval, allowedAreas: allowedAreas || "所有区域" });

    try {
      while (this.isRunning) {
        try {
          this.emit('log', "正在获取订单列表...");
          this.logger.info("正在获取订单列表...");

          // 获取订单列表
          const orderResponse = await this.getOrderList(uid, token);
          this.logger.info("获取订单列表成功", orderResponse);
          const orders = orderResponse && Array.isArray(orderResponse.data) ? orderResponse.data : [];

          if (orders.length > 0) {
            this.emit('log', `发现 ${orders.length} 个订单，开始过滤和抢单`);
            this.logger.info(`发现 ${orders.length} 个订单，开始过滤和抢单`);

            // 不允许的商品ID列表（黑名单）
            const blockedGoodsIds = ["73", "74", "88"];

            // 过滤订单
            const filteredOrders = orders.filter((order) => {
              const area = order.game_area;
              const goodsId = String(order.goods_id);

              // 检查区域
              if (allowedAreas && !allowedAreas.includes(area)) {
                this.emit('log', `🚫 过滤掉订单 ${order.id}，区域: ${area}（只抢${allowedAreas.join('/')}区域订单）`);
                return false;
              }

              this.emit('log', `✅ 订单 ${order.id} 通过过滤，区域: ${area}，商品ID: ${goodsId}`);
              return true;
            });

            this.emit('log', `过滤后剩余 ${filteredOrders.length} 个符合条件的订单`);

            // 显示订单详情
            filteredOrders.forEach((order, index) => {
              const orderInfo = {
                id: order.id,
                title: order.gg_title,
                area: order.game_area,
                goods_id: order.goods_id,
                updateTime: order.update_time,
              };
              this.emit('orderFound', orderInfo);
              this.logger.info(`符合条件的订单 ${index + 1}:`, orderInfo);
            });

            // 尝试抢单
            for (const order of filteredOrders) {
              if (!this.isRunning) break;

              const orderId = order.id;
              if (!orderId) {
                this.emit('log', "订单ID为空，跳过");
                continue;
              }

              this.emit('log', `开始抢单: ${orderId}`);
              this.grabAttempts = 0;

              // 多次尝试抢单
              while (this.grabAttempts < this.maxGrabAttempts && this.isRunning) {
                try {
                  this.grabAttempts++;
                  this.emit('log', `第 ${this.grabAttempts} 次尝试抢单: ${orderId}`);

                  const grabResponse = await this.grabOrder(orderId, token);

                  // 检查抢单是否成功
                  if (grabResponse && grabResponse.code === 0 && grabResponse.msg === "接单成功") {
                    this.emit('success', {
                      orderId,
                      message: "抢单成功！",
                      data: grabResponse,
                      playSound: true // 添加播放音效标志
                    });
                    this.logger.info(`🎉 抢单成功！订单ID: ${orderId}`, { response: grabResponse });
                    this.stop();
                    return { success: true, orderId, message: "抢单成功", data: grabResponse };
                  } else {
                    this.emit('log', `❌ 抢单失败，第 ${this.grabAttempts} 次尝试: ${grabResponse?.msg || grabResponse?.message}`);

                    // 根据响应决定处理策略
                    if (grabResponse && grabResponse.msg === "单子没接到") {
                      this.emit('log', `💔 订单 ${orderId} 单子没接到，重新获取订单`);
                      break;
                    } else if (grabResponse && (
                      grabResponse.msg?.includes("已被") ||
                      grabResponse.msg?.includes("不存在") ||
                      grabResponse.msg?.includes("已接") ||
                      grabResponse.message?.includes("已被") ||
                      grabResponse.message?.includes("不存在") ||
                      grabResponse.message?.includes("已接")
                    )) {
                      this.emit('log', `💔 订单 ${orderId} 已被其他人抢走，尝试下一个订单`);
                      break;
                    }
                  }
                } catch (error) {
                  this.emit('log', `抢单异常，第 ${this.grabAttempts} 次尝试: ${error.message}`);
                }
              }

              if (this.grabAttempts >= this.maxGrabAttempts) {
                this.emit('log', `订单 ${orderId} 抢单失败，已达到最大尝试次数`);
              }
            }

            if (filteredOrders.length === 0) {
              const areaText = allowedAreas ? `${allowedAreas.join('/')}区域` : "所有区域";
              this.emit('log', `过滤后暂无符合条件的订单可抢（${areaText}）`);
            }
          } else {
            this.emit('log', "暂无可抢订单");
          }

          // 等待指定间隔
          if (this.isRunning) {
            this.emit('log', `等待 ${interval}ms 后继续获取订单...`);
            await new Promise((resolve) => setTimeout(resolve, interval));
          }
        } catch (error) {
          this.emit('log', `自动抢单过程中发生错误: ${error.message}`);
          this.logger.error("自动抢单过程中发生错误", { error: error.message });

          if (this.isRunning) {
            await new Promise((resolve) => setTimeout(resolve, interval));
          }
        }
      }
    } finally {
      this.isRunning = false;
      this.emit('statusChange', { isRunning: false, message: "自动抢单已停止" });
      this.logger.info("自动抢单结束");
    }

    return { success: false, message: "自动抢单结束" };
  }

  // 停止抢单
  stop() {
    this.isRunning = false;
    this.emit('statusChange', { isRunning: false, message: "正在停止抢单..." });
    this.logger.info("收到停止抢单请求");
  }

  // 获取运行状态
  getStatus() {
    return {
      isRunning: this.isRunning,
      grabAttempts: this.grabAttempts,
      maxGrabAttempts: this.maxGrabAttempts
    };
  }
}

module.exports = OrderGrabber;
