<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
    <title>智能抢单助手</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        h2 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #fff;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
            box-sizing: border-box;
        }

        input::placeholder, textarea::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-running {
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        .status-stopped {
            background: #f44336;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .log-container {
            height: 300px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 5px;
            word-wrap: break-word;
        }

        .log-success {
            color: #4CAF50;
        }

        .log-error {
            color: #f44336;
        }

        .log-info {
            color: #2196F3;
        }

        button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: all 0.3s ease;
            width: 100%;
        }

        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .btn-start {
            background: rgba(76, 175, 80, 0.3);
            border-color: rgba(76, 175, 80, 0.5);
        }

        .btn-stop {
            background: rgba(244, 67, 54, 0.3);
            border-color: rgba(244, 67, 54, 0.5);
        }

        .btn-clear {
            background: rgba(255, 193, 7, 0.3);
            border-color: rgba(255, 193, 7, 0.5);
        }

        .btn-test {
            background: rgba(33, 150, 243, 0.3);
            border-color: rgba(33, 150, 243, 0.5);
        }

        .orders-container {
            max-height: 200px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 10px;
            margin-top: 10px;
        }

        .order-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .order-item:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 智能抢单助手</h1>
        <p>
            <span class="status-indicator" id="status-indicator"></span>
            <span id="status-text">未运行</span>
        </p>
    </div>

    <div class="container">
        <!-- 配置面板 -->
        <div class="panel">
            <h2>⚙️ 抢单配置</h2>

            <div class="form-group">
                <label for="uid">用户ID (UID):</label>
                <input type="text" id="uid" placeholder="请输入您的用户ID" required>
            </div>

            <div class="form-group">
                <label for="token">授权Token:</label>
                <input type="text" id="token" placeholder="请输入您的授权Token" required>
            </div>

            <div class="form-group">
                <label for="interval">检查间隔 (毫秒):</label>
                <input type="number" id="interval" value="5000" min="1000" max="60000">
            </div>

            <div class="form-group">
                <label for="areas">允许区域 (可选，用逗号分隔):</label>
                <input type="text" id="areas" placeholder="QQ 或者 微信">
            </div>

            <div class="form-group">
                <button id="test-token-btn" class="btn-test" onclick="testToken()">🔍 测试Token</button>
                <button class="btn-test" onclick="playSuccessSound()">🔊 测试音效</button>
                <button id="start-btn" class="btn-start" onclick="startGrabbing()">🚀 开始抢单</button>
                <button id="stop-btn" class="btn-stop" onclick="stopGrabbing()" disabled>⏹️ 停止抢单</button>
                <button class="btn-clear" onclick="clearLogs()">🗑️ 清空日志</button>
            </div>
        </div>

        <!-- 日志面板 -->
        <div class="panel">
            <h2>📋 运行日志</h2>
            <div class="log-container" id="log-container">
                <div class="log-entry log-info">等待开始抢单...</div>
            </div>

            <h2>📦 发现的订单</h2>
            <div class="orders-container" id="orders-container">
                <div class="order-item">暂无订单</div>
            </div>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        let isGrabbing = false;

        // 添加日志
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;

            const timestamp = new Date().toLocaleTimeString();
            logEntry.textContent = `[${timestamp}] ${message}`;

            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;

            // 限制日志条数
            const logs = logContainer.children;
            if (logs.length > 100) {
                logContainer.removeChild(logs[0]);
            }
        }

        // 更新状态
        function updateStatus(isRunning, message) {
            const indicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            const startBtn = document.getElementById('start-btn');
            const stopBtn = document.getElementById('stop-btn');

            if (isRunning) {
                indicator.className = 'status-indicator status-running';
                statusText.textContent = '运行中';
                startBtn.disabled = true;
                stopBtn.disabled = false;
            } else {
                indicator.className = 'status-indicator status-stopped';
                statusText.textContent = '已停止';
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }

            if (message) {
                addLog(message, isRunning ? 'success' : 'info');
            }
        }

        // 添加订单到列表
        function addOrder(order) {
            const ordersContainer = document.getElementById('orders-container');

            // 清空"暂无订单"提示
            if (ordersContainer.children.length === 1 && ordersContainer.children[0].textContent === '暂无订单') {
                ordersContainer.innerHTML = '';
            }

            const orderItem = document.createElement('div');
            orderItem.className = 'order-item';
            orderItem.innerHTML = `
                <strong>订单 ${order.id}</strong><br>
                标题: ${order.title || '未知'}<br>
                区域: ${order.area || '未知'}<br>
                商品ID: ${order.goods_id || '未知'}<br>
                更新时间: ${order.updateTime || '未知'}
            `;

            ordersContainer.appendChild(orderItem);

            // 限制订单显示数量
            const orders = ordersContainer.children;
            if (orders.length > 10) {
                ordersContainer.removeChild(orders[0]);
            }
        }

        // 测试Token有效性
        function testToken() {
            const token = document.getElementById('token').value.trim();

            if (!token) {
                alert('请先填写授权Token！');
                return;
            }

            const testBtn = document.getElementById('test-token-btn');
            testBtn.disabled = true;
            testBtn.textContent = '🔍 测试中...';

            addLog('正在测试Token有效性...', 'info');

            // 发送测试命令到主进程
            ipcRenderer.send('test-token', { token });
        }

        // 开始抢单
        function startGrabbing() {
            const uid = document.getElementById('uid').value.trim();
            const token = document.getElementById('token').value.trim();
            const interval = parseInt(document.getElementById('interval').value) || 5000;
            const areasInput = document.getElementById('areas').value.trim();
            const allowedAreas = areasInput ? areasInput.split(',').map(s => s.trim()).filter(s => s) : null;

            if (!uid || !token) {
                alert('请填写用户ID和授权Token！');
                return;
            }

            isGrabbing = true;
            updateStatus(true, '正在启动抢单程序...');

            // 发送启动命令到主进程
            ipcRenderer.send('start-grabbing', { uid, token, interval, allowedAreas });
        }

        // 停止抢单
        function stopGrabbing() {
            isGrabbing = false;
            updateStatus(false, '正在停止抢单程序...');

            // 发送停止命令到主进程
            ipcRenderer.send('stop-grabbing');
        }

        // 清空日志
        function clearLogs() {
            const logContainer = document.getElementById('log-container');
            logContainer.innerHTML = '<div class="log-entry log-info">日志已清空</div>';

            const ordersContainer = document.getElementById('orders-container');
            ordersContainer.innerHTML = '<div class="order-item">暂无订单</div>';
        }

        // 播放成功音效
        function playSuccessSound() {
            try {
                // 创建音频上下文
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();

                // 播放成功提示音序列 - 模拟胜利音效
                playVictorySound(audioContext);

                addLog('🔊 播放成功音效', 'success');

            } catch (error) {
                console.log('音频播放失败:', error);
                // 降级到简单的提示
                addLog('🎉 抢单成功！（音效播放失败）', 'success');
            }
        }

        // 播放胜利音效
        function playVictorySound(audioContext) {
            // 胜利音效序列 - 模拟经典的成功音效
            const notes = [
                { freq: 523.25, duration: 200 }, // C5
                { freq: 659.25, duration: 200 }, // E5
                { freq: 783.99, duration: 200 }, // G5
                { freq: 1046.5, duration: 400 }  // C6 (长音)
            ];

            let delay = 0;
            notes.forEach((note, index) => {
                setTimeout(() => {
                    playNote(audioContext, note.freq, note.duration);
                }, delay);
                delay += note.duration + 50; // 音符间隔
            });

            // 添加和弦效果
            setTimeout(() => {
                playChord(audioContext, [523.25, 659.25, 783.99], 600);
            }, delay + 200);
        }

        // 播放单个音符
        function playNote(audioContext, frequency, duration) {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = 'triangle'; // 使用三角波，声音更柔和

            // 音量包络 - 快速上升，缓慢下降
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration / 1000);
        }

        // 播放和弦
        function playChord(audioContext, frequencies, duration) {
            frequencies.forEach(freq => {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.value = freq;
                oscillator.type = 'sine';

                gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                gainNode.gain.linearRampToValueAtTime(0.15, audioContext.currentTime + 0.01);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + duration / 1000);
            });
        }

        // 显示成功通知
        function showSuccessNotification(orderId) {
            // 创建全屏通知
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(76, 175, 80, 0.9);
                color: white;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                font-size: 3em;
                font-weight: bold;
                z-index: 10000;
                animation: pulse 0.5s ease-in-out;
            `;

            notification.innerHTML = `
                <div style="text-align: center;">
                    <div style="font-size: 4em; margin-bottom: 20px;">🎉</div>
                    <div>抢单成功！</div>
                    <div style="font-size: 0.6em; margin-top: 20px;">订单ID: ${orderId}</div>
                    <div style="font-size: 0.4em; margin-top: 20px; opacity: 0.8;">点击任意位置关闭</div>
                </div>
            `;

            // 添加动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes pulse {
                    0% { transform: scale(0.8); opacity: 0; }
                    50% { transform: scale(1.1); opacity: 1; }
                    100% { transform: scale(1); opacity: 1; }
                }
            `;
            document.head.appendChild(style);

            // 添加到页面
            document.body.appendChild(notification);

            // 点击关闭
            notification.addEventListener('click', () => {
                document.body.removeChild(notification);
                document.head.removeChild(style);
            });

            // 5秒后自动关闭
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                    document.head.removeChild(style);
                }
            }, 5000);
        }

        // 监听主进程消息
        ipcRenderer.on('grabber-log', (event, message) => {
            addLog(message, 'info');
        });

        ipcRenderer.on('grabber-error', (event, message) => {
            addLog(message, 'error');
        });

        ipcRenderer.on('grabber-success', (event, data) => {
            addLog(`🎉 抢单成功！订单ID: ${data.orderId}`, 'success');
            updateStatus(false, '抢单成功，程序已停止');

            // 播放成功音效
            playSuccessSound();

            // 显示成功提示
            showSuccessNotification(data.orderId);
        });

        ipcRenderer.on('grabber-status', (event, data) => {
            updateStatus(data.isRunning, data.message);
        });

        ipcRenderer.on('grabber-order-found', (event, order) => {
            addOrder(order);
        });

        ipcRenderer.on('token-test-result', (event, result) => {
            const testBtn = document.getElementById('test-token-btn');
            testBtn.disabled = false;
            testBtn.textContent = '🔍 测试Token';

            if (result.valid) {
                addLog(`✅ Token验证成功: ${result.message}`, 'success');
            } else {
                addLog(`❌ Token验证失败: ${result.message} (${result.reason})`, 'error');
            }
        });

        // 监听播放成功音效命令
        ipcRenderer.on('play-success-sound', () => {
            playSuccessSound();
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            addLog('智能抢单助手已启动，请配置参数后开始抢单', 'success');

            // 添加一些动画效果
            const container = document.querySelector('.container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';

            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });

        // 键盘快捷键
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey || event.metaKey) {
                switch (event.key) {
                    case 'r':
                        event.preventDefault();
                        location.reload();
                        break;
                    case 'i':
                        event.preventDefault();
                        require('electron').remote?.getCurrentWindow().webContents.openDevTools();
                        break;
                    case 's':
                        event.preventDefault();
                        if (!isGrabbing) {
                            startGrabbing();
                        } else {
                            stopGrabbing();
                        }
                        break;
                }
            }
        });
    </script>
</body>
</html>
