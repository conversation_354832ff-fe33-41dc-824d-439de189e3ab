const { app, BrowserWindow, Menu, ipcMain, shell } = require('electron');
const path = require('path');
const OrderGrabber = require('./grabber');

// 保持对窗口对象的全局引用，如果不这么做的话，当 JavaScript 对象被
// 垃圾回收的时候，窗口会被自动地关闭
let mainWindow;
let orderGrabber;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true, // 临时启用以简化调试
      contextIsolation: false, // 临时禁用以简化调试
      enableRemoteModule: true // 临时启用以简化调试
    },
    icon: path.join(__dirname, 'assets/icon.png'), // 可选：应用图标
    show: false, // 先不显示窗口，等加载完成后再显示
    titleBarStyle: 'default' // 使用默认标题栏，确保窗口可以拖动
  });

  // 加载应用的 index.html
  mainWindow.loadFile('index.html');

  // 当窗口准备好显示时再显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // 可选：在开发环境下打开开发者工具
    if (process.env.NODE_ENV === 'development') {
      mainWindow.webContents.openDevTools();
    }
  });

  // 当窗口被关闭时发出 closed 事件
  mainWindow.on('closed', () => {
    // 取消引用 window 对象，如果你的应用支持多窗口的话，
    // 通常会把多个 window 对象存放在一个数组里面，
    // 与此同时，你应该删除相应的元素。
    mainWindow = null;
  });

  // 处理外部链接
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    require('electron').shell.openExternal(url);
    return { action: 'deny' };
  });

  // 初始化抢单器
  orderGrabber = new OrderGrabber();

  // 设置抢单器事件监听
  orderGrabber.on('log', (message) => {
    mainWindow.webContents.send('grabber-log', message);
  });

  orderGrabber.on('error', (message) => {
    mainWindow.webContents.send('grabber-error', message);
  });

  orderGrabber.on('success', (data) => {
    mainWindow.webContents.send('grabber-success', data);

    // 播放系统提示音
    if (data.playSound) {
      shell.beep();

      // 发送播放音效命令到渲染进程
      mainWindow.webContents.send('play-success-sound');

      // 显示系统通知
      if (process.platform === 'darwin') {
        // macOS 通知
        const { Notification } = require('electron');
        if (Notification.isSupported()) {
          new Notification({
            title: '🎉 抢单成功！',
            body: `订单 ${data.orderId} 抢单成功！`,
            sound: true
          }).show();
        }
      }
    }
  });

  orderGrabber.on('statusChange', (data) => {
    mainWindow.webContents.send('grabber-status', data);
  });

  orderGrabber.on('orderFound', (order) => {
    mainWindow.webContents.send('grabber-order-found', order);
  });
}

// Electron 会在初始化后并准备
// 创建浏览器窗口时，调用这个函数。
// 部分 API 在 ready 事件触发后才能使用。
app.whenReady().then(() => {
  createWindow();

  // 在 macOS 上，当点击 dock 图标并且没有其他窗口打开的时候，
  // 通常在应用程序中重新创建一个窗口。
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });

  // 创建应用菜单
  createMenu();
});

// 当全部窗口关闭时退出。
app.on('window-all-closed', () => {
  // 在 macOS 上，除非用户用 Cmd + Q 确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 在这个文件中，你可以续写应用剩下主进程代码。
// 也可以拆分成几个文件，然后用 require 导入。

function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // 处理新建文件逻辑
            console.log('New file');
          }
        },
        {
          label: 'Open',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            // 处理打开文件逻辑
            console.log('Open file');
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' }
      ]
    }
  ];

  // macOS 特殊处理
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    });

    // Window menu
    template[4].submenu = [
      { role: 'close' },
      { role: 'minimize' },
      { role: 'zoom' },
      { type: 'separator' },
      { role: 'front' }
    ];
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// IPC 通信处理
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

// 处理开始抢单
ipcMain.on('start-grabbing', async (event, config) => {
  try {
    const { uid, token, interval, allowedAreas } = config;
    console.log('开始抢单:', config);

    if (orderGrabber) {
      await orderGrabber.startAutoGrab(uid, token, interval, allowedAreas);
    }
  } catch (error) {
    console.error('启动抢单失败:', error);
    mainWindow.webContents.send('grabber-error', `启动失败: ${error.message}`);
  }
});

// 处理停止抢单
ipcMain.on('stop-grabbing', (event) => {
  try {
    console.log('停止抢单');
    if (orderGrabber) {
      orderGrabber.stop();
    }
  } catch (error) {
    console.error('停止抢单失败:', error);
    mainWindow.webContents.send('grabber-error', `停止失败: ${error.message}`);
  }
});

// 处理测试Token
ipcMain.on('test-token', async (event, config) => {
  try {
    const { token } = config;
    console.log('测试Token有效性');

    if (orderGrabber) {
      const result = await orderGrabber.validateToken(token);
      mainWindow.webContents.send('token-test-result', result);
    }
  } catch (error) {
    console.error('测试Token失败:', error);
    mainWindow.webContents.send('token-test-result', {
      valid: false,
      code: -1,
      message: error.message,
      reason: "测试过程异常"
    });
  }
});

// 处理应用退出前的清理工作
app.on('before-quit', (event) => {
  // 在这里可以添加退出前的清理逻辑
  console.log('Application is about to quit');
});
