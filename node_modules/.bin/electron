#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/未命名文件夹/node_modules/.pnpm/electron@36.4.0/node_modules/electron/node_modules:/Users/<USER>/Desktop/未命名文件夹/node_modules/.pnpm/electron@36.4.0/node_modules:/Users/<USER>/Desktop/未命名文件夹/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/未命名文件夹/node_modules/.pnpm/electron@36.4.0/node_modules/electron/node_modules:/Users/<USER>/Desktop/未命名文件夹/node_modules/.pnpm/electron@36.4.0/node_modules:/Users/<USER>/Desktop/未命名文件夹/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/electron@36.4.0/node_modules/electron/cli.js" "$@"
else
  exec node  "$basedir/../.pnpm/electron@36.4.0/node_modules/electron/cli.js" "$@"
fi
