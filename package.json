{"name": "my-electron-app", "version": "1.0.0", "description": "E", "main": "main.js", "scripts": {"start": "electron .", "dev": "NODE_ENV=development electron .", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "dist": "npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.0", "winston": "^3.11.0"}, "devDependencies": {"electron": "^36.4.0", "electron-builder": "^26.0.12"}, "build": {"appId": "com.grabber.smartorder", "productName": "智能抢单助手", "directories": {"output": "dist"}, "files": ["main.js", "index.html", "preload.js", "grabber.js", "package.json", "assets/**/*", "node_modules/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "智能抢单助手"}, "mac": {"target": "dmg", "category": "public.app-category.productivity"}, "linux": {"target": "AppImage", "category": "Utility"}}}