const OrderGrabber = require('./grabber');

async function testGrabber() {
  const grabber = new OrderGrabber();
  
  console.log("🧪 测试抢单器功能...");
  
  // 监听事件
  grabber.on('log', (message) => {
    console.log(`[LOG] ${message}`);
  });
  
  grabber.on('error', (message) => {
    console.log(`[ERROR] ${message}`);
  });
  
  grabber.on('statusChange', (data) => {
    console.log(`[STATUS] ${data.message} (运行中: ${data.isRunning})`);
  });
  
  // 测试Token验证功能（独立测试）
  console.log("\n1️⃣ 测试Token验证功能:");
  const testToken = "invalid_token_for_test";
  const tokenResult = await grabber.validateToken(testToken);
  console.log("Token验证结果:", tokenResult);
  
  // 测试抢单启动（不会自动验证Token）
  console.log("\n2️⃣ 测试抢单启动（应该直接开始，不验证Token）:");
  try {
    // 这里会立即开始抢单流程，不会先验证Token
    setTimeout(() => {
      grabber.stop();
      console.log("\n✅ 测试完成！Token验证已独立，抢单流程不再自动验证Token");
    }, 2000);
    
    await grabber.startAutoGrab("test_uid", "test_token", 10000, null);
  } catch (error) {
    console.log("抢单测试结果:", error.message);
  }
}

testGrabber().catch(console.error);
