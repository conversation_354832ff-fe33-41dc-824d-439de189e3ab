const OrderGrabber = require('./grabber');

async function testTokenValidation() {
  const grabber = new OrderGrabber();
  
  // 测试您提供的Token（已过期的示例）
  const testToken = "OGQzZGQ3YY4ZDllMzQzZTVkZTBjYTQ2ZTVkZDdjODdfNWU3YTQ2MjM5ZTFhNmFhODBiNTMxZDc3NzFkZWEyZDU=";
  
  console.log("🔍 开始测试Token验证功能...");
  console.log("测试Token:", testToken);
  console.log("=".repeat(50));
  
  try {
    const result = await grabber.validateToken(testToken);
    
    console.log("📋 验证结果:");
    console.log("- 有效性:", result.valid ? "✅ 有效" : "❌ 无效");
    console.log("- 状态码:", result.code);
    console.log("- 消息:", result.message);
    console.log("- 原因:", result.reason || "无");
    
    if (result.data) {
      console.log("- 响应数据:", JSON.stringify(result.data, null, 2));
    }
    
  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error.message);
  }
  
  console.log("\n🎯 测试完成！");
}

// 运行测试
testTokenValidation();
