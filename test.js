const express = require("express");
const axios = require("axios");
const winston = require("winston");
const app = express();
const port = 3000;

// 配置日志记录器
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: "error.log", level: "error" }),
    new winston.transports.File({ filename: "combined.log" }),
    new winston.transports.Console({
      format: winston.format.simple(),
    }),
  ],
});

// 中间件
app.use(express.json());

// 获取订单
async function getOrderList(uid, token) {
  try {
    const response = await axios.request({
      method: "GET",
      url: "https://api.9100dianjing.cn/wxmini/my/partner/list/new",
      params: { uid, type: "" },
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c33)XWEB/13639",
        "x-pwid": "9100",
        xweb_xhr: "1",
        authorization: `Bearer ${token}`,
        "content-type": "application/json",
        "sec-fetch-site": "cross-site",
        "sec-fetch-mode": "cors",
        "sec-fetch-dest": "empty",
        referer:
          "https://servicewechat.com/wx7ff158ed3cf27972/14/page-frame.html",
        "accept-language": "zh-CN,zh;q=0.9",
        priority: "u=1, i",
        Accept: "*/*",
        "Accept-Encoding": "gzip, deflate, br",
        Connection: "keep-alive",
        "Cache-Control": "no-cache",
        Host: "api.9100dianjing.cn",
      },
    });
    return response.data;
  } catch (error) {
    logger.error("获取订单列表失败:", { error: error.message });
    throw error;
  }
}

// 抢单
async function grabOrder(orderId, token) {
  try {
    const response = await axios.request({
      method: "POST",
      url: "https://api.9100dianjing.cn/wxmini/my/partner/getorder",
      headers: {
        Referer:
          "https://servicewechat.com/wx7ff158ed3cf27972/14/page-frame.html",
        Authorization: `Bearer ${token}`,
        "X-Pwid": "9100",
        Host: "api.9100dianjing.cn",
        "User-Agent":
          "Mozilla/5.0 (iPad; CPU OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.59(0x18003b2e) NetType/WIFI Language/zh_CN",
        Accept: "*/*",
        "Accept-Encoding": "gzip, deflate, br",
        Connection: "keep-alive",
        "Content-Type": "application/json",
        "Cache-Control": "no-cache",
      },
      data: { order_id: orderId },
    });
    return response.data;
  } catch (error) {
    logger.error("抢单失败:", { error: error.message, orderId });
    throw error;
  }
}

// 自动抢单函数
async function autoGrabOrders(uid, token, interval = 5000, allowedAreas = null) {
  logger.info("开始自动抢单", { uid, interval, allowedAreas: allowedAreas || "所有区域" });

  let isRunning = true;
  let grabAttempts = 0;
  const maxGrabAttempts = 300; // 每个订单最多尝试抢单次数

  while (isRunning) {
    try {
      logger.info("正在获取订单列表...");

      // 获取订单列表
      const orderResponse = await getOrderList(uid, token);

      // 只通过data数组是否有订单来判断是否有可抢订单
      // 不依赖code、count等其他字段
      const orders =
        orderResponse && Array.isArray(orderResponse.data)
          ? orderResponse.data
          : [];

      if (orders.length > 0) {
        logger.info(`发现 ${orders.length} 个订单，开始过滤和抢单`);

        // 允许的商品ID列表
        //const allowedGoodsIds = ["51", "86", "84", "85", "52"];
        // 不允许的商品ID列表（黑名单）
        const blockedGoodsIds = ["73", "74", "88"];

        // 过滤订单：只保留指定区域且商品ID符合条件的订单
        const filteredOrders = orders.filter((order) => {
          const area = order.game_area;
          const goodsId = String(order.goods_id); // 确保转换为字符串进行比较

          // 检查区域（如果指定了allowedAreas才进行区域过滤）
          if (allowedAreas && !allowedAreas.includes(area)) {
            logger.info(`🚫 过滤掉订单 ${order.id}，区域: ${area}（只抢${allowedAreas.join('/')}区域订单）`);
            return false;
          }

          // 检查商品ID（黑名单）
          // if (blockedGoodsIds.includes(goodsId)) {
          //   logger.info(
          //     `🚫 过滤掉订单 ${order.id}，商品ID: ${goodsId}（在黑名单中）`
          //   );
          //   return false;
          // }

          // // 检查商品ID（白名单）
          // if (!allowedGoodsIds.includes(goodsId)) {
          //   logger.info(
          //     `🚫 过滤掉订单 ${order.id}，商品ID: ${goodsId}（不在白名单中）`
          //   );
          //   return false;
          // }

          logger.info(
            `✅ 订单 ${order.id} 通过过滤，区域: ${area}，商品ID: ${goodsId}`
          );
          return true;
        });

        logger.info(`过滤后剩余 ${filteredOrders.length} 个符合条件的订单`);

        // 打印过滤后的订单详情
        filteredOrders.forEach((order, index) => {
          logger.info(`符合条件的订单 ${index + 1}:`, {
            id: order.id,
            title: order.gg_title,
            area: order.game_area,
            goods_id: order.goods_id,
            updateTime: order.update_time,
          });
        });

        // 遍历过滤后的订单，尝试抢单
        for (const order of filteredOrders) {
          if (!isRunning) break;

          const orderId = order.id;
          if (!orderId) {
            logger.warn("订单ID为空，跳过", { order });
            continue;
          }

          logger.info(`开始抢单: ${orderId}`);
          grabAttempts = 0;

          // 多次尝试抢单，直到成功或达到最大尝试次数
          while (grabAttempts < maxGrabAttempts && isRunning) {
            try {
              grabAttempts++;
              logger.info(`第 ${grabAttempts} 次尝试抢单: ${orderId}`);

              const grabResponse = await grabOrder(orderId, token);

              // 检查抢单是否成功通过msg来判断
              //     // 成功示例: {"code":0,"msg":"接单成功","data":[],"count":0,"other":[],"server":"10.23.55.8"}
              if (
                grabResponse &&
                grabResponse.code === 0 &&
                grabResponse.msg === "接单成功"
              ) {
                logger.info(`🎉 抢单成功！订单ID: ${orderId}`, {
                  response: grabResponse,
                });
                isRunning = false; // 抢到订单后退出循环
                return {
                  success: true,
                  orderId,
                  message: "抢单成功",
                  data: grabResponse,
                };
              } else {
                logger.warn(`❌ 抢单失败，第 ${grabAttempts} 次尝试`, {
                  orderId,
                  code: grabResponse?.code,
                  msg: grabResponse?.msg,
                  message: grabResponse?.message,
                });

                // 根据msg内容决定处理策略
                if (grabResponse && grabResponse.msg === "单子没接到") {
                  logger.info(
                    `💔 订单 ${orderId} 单子没接到，退出抢单环节，重新获取订单`
                  );
                  break; // 跳出当前订单的抢单循环，回到获取订单步骤
                } else if (
                  grabResponse &&
                  (grabResponse.msg?.includes("已被") ||
                    grabResponse.msg?.includes("不存在") ||
                    grabResponse.msg?.includes("已接") ||
                    grabResponse.message?.includes("已被") ||
                    grabResponse.message?.includes("不存在") ||
                    grabResponse.message?.includes("已接"))
                ) {
                  logger.info(
                    `💔 订单 ${orderId} 已被其他人抢走，尝试下一个订单`
                  );
                  break;
                }

                // 其他情况（如"再试一次"）继续重试，不需要延迟
              }
            } catch (error) {
              logger.error(`抢单异常，第 ${grabAttempts} 次尝试`, {
                orderId,
                error: error.message,
              });

              // 抢单异常时也不需要延迟，立即重试
            }
          }

          if (grabAttempts >= maxGrabAttempts) {
            logger.warn(`订单 ${orderId} 抢单失败，已达到最大尝试次数`);
          }
        }

        // 如果过滤后没有符合条件的订单
        if (filteredOrders.length === 0) {
          const areaText = allowedAreas ? `${allowedAreas.join('/')}区域` : "所有区域";
          logger.info(
            `过滤后暂无符合条件的订单可抢（${areaText}且商品ID为51/86/84/85/52）`
          );
        }
      } else {
        logger.info("暂无可抢订单");
      }

      // 如果还在运行，等待指定间隔后继续
      if (isRunning) {
        logger.info(`等待 ${interval}ms 后继续获取订单...`);
        await new Promise((resolve) => setTimeout(resolve, interval));
      }
    } catch (error) {
      logger.error("自动抢单过程中发生错误", { error: error.message });

      // 发生错误时也要等待一段时间再重试
      if (isRunning) {
        await new Promise((resolve) => setTimeout(resolve, interval));
      }
    }
  }

  logger.info("自动抢单结束");
  return { success: false, message: "自动抢单结束" };
}

// 全局变量存储运行状态
let autoGrabProcess = null;

// API路由
app.post("/start-auto-grab", async (req, res) => {
  const {
    uid,
    token,
    interval = 5000,
    allowedAreas = null
  } = req.body;

  if (!uid || !token) {
    return res.status(400).json({ error: "缺少必要参数 uid 和 token" });
  }

  if (autoGrabProcess) {
    return res.status(400).json({ error: "自动抢单已在运行中" });
  }

  // 验证区域参数（如果提供的话）
  if (allowedAreas !== null && (!Array.isArray(allowedAreas) || allowedAreas.length === 0)) {
    return res.status(400).json({ error: "allowedAreas 必须是非空数组或null" });
  }

  try {
    logger.info("启动自动抢单", { uid, interval, allowedAreas });

    // 启动自动抢单（异步执行）
    autoGrabProcess = autoGrabOrders(uid, token, interval, allowedAreas)
      .then((result) => {
        logger.info("自动抢单完成", result);
        autoGrabProcess = null;
        return result;
      })
      .catch((error) => {
        logger.error("自动抢单异常结束", { error: error.message });
        autoGrabProcess = null;
        throw error;
      });

    res.json({
      message: "自动抢单已启动",
      params: { uid, interval, allowedAreas },
    });
  } catch (error) {
    logger.error("启动自动抢单失败", { error: error.message });
    autoGrabProcess = null;
    res.status(500).json({ error: "启动失败: " + error.message });
  }
});

// 停止自动抢单
app.post("/stop-auto-grab", (req, res) => {
  if (!autoGrabProcess) {
    return res.status(400).json({ error: "自动抢单未在运行" });
  }

  try {
    // 这里可以添加停止逻辑，比如设置全局标志
    logger.info("收到停止自动抢单请求");
    res.json({ message: "停止请求已发送" });
  } catch (error) {
    logger.error("停止自动抢单失败", { error: error.message });
    res.status(500).json({ error: "停止失败: " + error.message });
  }
});

// 获取自动抢单状态
app.get("/auto-grab-status", (req, res) => {
  res.json({
    isRunning: !!autoGrabProcess,
    message: autoGrabProcess ? "自动抢单运行中" : "自动抢单未运行",
  });
});

// 健康检查端点
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.version
  });
});

// 启动服务器
app.listen(port, () => {
  logger.info(`服务器运行在 http://localhost:${port}`);
});
